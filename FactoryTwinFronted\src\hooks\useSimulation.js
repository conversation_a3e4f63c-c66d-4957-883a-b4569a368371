import { useState, useEffect, useRef, useMemo } from 'react';
import { findPath } from '../utils/pathfinding';
// ====================================================================================
// --- FILE: src/hooks/useSimulation.js ---
// 这个 Hook 管理所有动态方面：时间、播放和移动单元的位置。
// ====================================================================================
export const useSimulation = (mapData) => {
    const [currentTime, setCurrentTime] = useState(0);
    const [isPlaying, setIsPlaying] = useState(false);
    const [simulationData, setSimulationData] = useState({ cars: [], pallets: [], route: null });
    const animationFrameId = useRef();

    // 主模拟循环
    useEffect(() => {
        const animate = () => {
            setCurrentTime(prevTime => {
                const routeDuration = simulationData.route?.totalTime || 0;
                if (prevTime >= routeDuration) {
                    setIsPlaying(false);
                    return routeDuration;
                }
                return prevTime + 1 / 60; // 平滑地推进时间
            });
            animationFrameId.current = requestAnimationFrame(animate);
        };

        if (isPlaying) {
            animationFrameId.current = requestAnimationFrame(animate);
        } else {
            cancelAnimationFrame(animationFrameId.current);
        }

        return () => cancelAnimationFrame(animationFrameId.current);
    }, [isPlaying, simulationData.route]);

    const calculateCarPosition = (route, time) => {
        if (!route || route.steps.length < 2) return null;

        const { steps } = route;

        // 找到小车当前所在的路段
        let startStep = null;
        let endStep = null;
        for (let i = 0; i < steps.length - 1; i++) {
            if (time >= steps[i].time && time <= steps[i + 1].time) {
                startStep = steps[i];
                endStep = steps[i + 1];
                break;
            }
        }

        // 如果时间超出了路线，将小车放在终点
        if (!startStep) {
            const lastStep = steps[steps.length-1];
            const beforeLastStep = steps[steps.length-2];
            const startNode = mapData.nodes.get(beforeLastStep.nodeId);
            const endNode = mapData.nodes.get(lastStep.nodeId);
            if (!startNode || !endNode) return null;
             return {
                x: endNode.x,
                y: endNode.y,
                angle: Math.atan2(endNode.y - startNode.y, endNode.x - startNode.x) + Math.PI / 2,
            };
        }

        const segmentDuration = endStep.time - startStep.time;
        const timeIntoSegment = time - startStep.time;
        const progress = segmentDuration > 0 ? timeIntoSegment / segmentDuration : 0;
        
        const startNode = mapData.nodes.get(startStep.nodeId);
        const endNode = mapData.nodes.get(endStep.nodeId);

        if (!startNode || !endNode) return null;

        const x = startNode.x + (endNode.x - startNode.x) * progress;
        const y = startNode.y + (endNode.y - startNode.y) * progress;
        const angle = Math.atan2(endNode.y - startNode.y, endNode.x - startNode.x) + Math.PI / 2; // 加 PI/2 使其朝向“上”

        return { x, y, angle };
    };

    // 这是基于当前时间的派生状态
    const dynamicElements = useMemo(() => {
        const carsWithPositions = simulationData.cars.map(car => {
            const position = calculateCarPosition(simulationData.route, currentTime);
            return { ...car, position };
        }).filter(car => car.position); // 过滤掉无法定位的小车

        // 托盘逻辑可以在这里扩展 - 为托盘添加位置信息
        const palletsWithPositions = simulationData.pallets.map(pallet => {
            // 如果托盘有位置信息，使用节点的坐标
            if (pallet.location && mapData.nodes.has(pallet.location)) {
                const locationNode = mapData.nodes.get(pallet.location);
                return {
                    ...pallet,
                    position: {
                        x: locationNode.x + (Math.random() - 0.5) * 20, // 添加一些随机偏移避免重叠
                        y: locationNode.y + (Math.random() - 0.5) * 20
                    }
                };
            }
            // 如果没有位置信息，使用默认位置或随机位置
            return {
                ...pallet,
                position: {
                    x: 50 + Math.random() * 100,
                    y: 50 + Math.random() * 100
                }
            };
        });

        return { cars: carsWithPositions, pallets: palletsWithPositions };

    }, [currentTime, simulationData, mapData.nodes]);

    // 初始化动态元素（车辆和托盘）
    useEffect(() => {
        if (!mapData || mapData.nodes.size === 0) return;

        // 使用地图数据中的车辆和托盘信息，如果没有则使用默认值
        const initialElements = {
            cars: mapData.vehicles && mapData.vehicles.length > 0
                ? mapData.vehicles.map(vehicle => ({
                    id: vehicle.id,
                    name: vehicle.type.replace('_', ' ').toUpperCase(),
                    status: vehicle.status,
                    battery: vehicle.battery_level,
                    location: vehicle.location
                }))
                : [
                    { id: 'car-01', name: 'AGV 1', status: 'idle', battery: 85 },
                    { id: 'car-02', name: 'AGV 2', status: 'active', battery: 92 }
                ],
            pallets: mapData.pallets && mapData.pallets.length > 0
                ? mapData.pallets.map(pallet => ({
                    id: pallet.id,
                    shape: pallet.shape || 'standard',
                    status: pallet.status,
                    contents: pallet.contents,
                    weight: pallet.weight,
                    location: pallet.location
                }))
                : [
                    { id: 'pallet-01', shape: 'standard', status: 'loaded' },
                    { id: 'pallet-02', shape: 'large', status: 'empty' },
                    { id: 'pallet-03', shape: 'long', status: 'in_transit' }
                ],
            route: null
        };

        setSimulationData(initialElements);
    }, [mapData]);

    const setRoute = (startNodeId, endNodeId) => {
        const route = findPath(mapData, startNodeId, endNodeId);
        if (route) {
            setSimulationData(prevData => ({
                ...prevData,
                route: route
            }));
            setCurrentTime(0);
            setIsPlaying(true);
        } else {
            // 处理未找到路径的情况
            setSimulationData(prevData => ({
                ...prevData,
                route: null
            }));
        }
    };

    const play = () => setIsPlaying(true);
    const pause = () => setIsPlaying(false);
    const setTime = (time) => {
        setIsPlaying(false);
        setCurrentTime(time);
    }
    
    return {
        currentTime,
        isPlaying,
        play,
        pause,
        setTime,
        setRoute,
        simulationData,
        dynamicElements
    };
};