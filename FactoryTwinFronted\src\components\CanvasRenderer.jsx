import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
    drawNode,
    drawUnidirectionalEdge,
    drawBidirectionalEdge,
    drawCar,
    drawPallet
} from '../utils/canvasUtils';

// ====================================================================================
// --- FILE: src/components/CanvasRenderer.js ---
// 处理所有 Canvas 渲染和交互的专用组件。
// ====================================================================================
export const CanvasRenderer = ({ mapData, dynamicElements, selectedElement, onElementClick }) => {
    const canvasRef = useRef(null);
    const containerRef = useRef(null);

    const [scale, setScale] = useState(1);
    const [offset, setOffset] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [isSpacePressed, setIsSpacePressed] = useState(false);
    const lastMousePosition = useRef({ x: 0, y: 0 });

    const MIN_SCALE = 0.01;
    const MAX_SCALE = 5;
    const ZOOM_SENSITIVITY = 0.001;

    // --- 绘图逻辑 ---
    const draw = useCallback(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;
        const ctx = canvas.getContext('2d');

        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.save();
        ctx.translate(offset.x, offset.y);
        ctx.scale(scale, scale);

        // 可见性阈值
        const ARROW_VISIBILITY_THRESHOLD = 0.05;
        const NODE_VISIBILITY_THRESHOLD = 0.03;

        const { nodes, unidirectional_edges, bidirectional_edges } = mapData;

        // 绘制双向边
        bidirectional_edges.forEach(edge => {
            const startNode = nodes.get(edge.nodes[0]);
            const endNode = nodes.get(edge.nodes[1]);
            if (startNode && endNode) {
                drawBidirectionalEdge(ctx, startNode, endNode, scale, selectedElement?.data?.id === edge.id);
            }
        });

        // 绘制单向边
        unidirectional_edges.forEach(edge => {
            const startNode = nodes.get(edge.start_node_id);
            const endNode = nodes.get(edge.end_node_id);
            if (startNode && endNode) {
                if (scale >= ARROW_VISIBILITY_THRESHOLD) {
                    drawUnidirectionalEdge(ctx, startNode, endNode, scale, selectedElement?.data?.id === edge.id);
                } else {
                    // 放大后绘制简单的线
                    ctx.strokeStyle = '#3B82F6';
                    ctx.lineWidth = 2 / scale;
                    ctx.beginPath();
                    ctx.moveTo(startNode.x, startNode.y);
                    ctx.lineTo(endNode.x, endNode.y);
                    ctx.stroke();
                }
            }
        });

        // 绘制节点
        if (scale >= NODE_VISIBILITY_THRESHOLD) {
            nodes.forEach(node => {
                drawNode(ctx, node, scale, selectedElement?.data?.id === node.id);
            });
        }

        // 绘制动态元素
        dynamicElements.pallets.forEach(pallet => drawPallet(ctx, pallet, scale));
        dynamicElements.cars.forEach(car => drawCar(ctx, car, scale));

        ctx.restore();
    }, [scale, offset, mapData, dynamicElements, selectedElement]);

    useEffect(() => {
        draw();
    }, [draw]);

    // --- 视图控制 (缩放, 平移, 重置) ---
    const resetView = useCallback(() => {
        const { nodes } = mapData;
        const container = containerRef.current;
        if (!container || nodes.size === 0) return;

        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        nodes.forEach(node => {
            if (node.x < minX) minX = node.x;
            if (node.y < minY) minY = node.y;
            if (node.x > maxX) maxX = node.x;
            if (node.y > maxY) maxY = node.y;
        });

        const { width: viewWidth, height: viewHeight } = container.getBoundingClientRect();
        const mapWidth = maxX - minX;
        const mapHeight = maxY - minY;
        const mapCenterX = minX + mapWidth / 2;
        const mapCenterY = minY + mapHeight / 2;

        const scaleX = mapWidth > 0 ? (viewWidth / mapWidth) * 0.9 : 1;
        const scaleY = mapHeight > 0 ? (viewHeight / mapHeight) * 0.9 : 1;
        const initialScale = Math.min(scaleX, scaleY, MAX_SCALE);

        setOffset({
            x: viewWidth / 2 - mapCenterX * initialScale,
            y: viewHeight / 2 - mapCenterY * initialScale,
        });
        setScale(initialScale);
    }, [mapData]);

    useEffect(() => {
        // 数据加载后自动重置视图
        if (mapData.nodes.size > 0) {
            resetView();
        }
    }, [mapData.nodes, resetView]);

    // --- 事件处理器 ---
    const handleWheel = useCallback((e) => {
        e.preventDefault();
        if (!canvasRef.current) return;
        const rect = canvasRef.current.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        const zoomFactor = 1 - e.deltaY * ZOOM_SENSITIVITY;
        const newScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale * zoomFactor));
        const worldX = (mouseX - offset.x) / scale;
        const worldY = (mouseY - offset.y) / scale;
        setOffset({ x: mouseX - worldX * newScale, y: mouseY - worldY * newScale });
        setScale(newScale);
    }, [scale, offset]);

    const handleMouseDown = useCallback((e) => {
        if (isSpacePressed) {
            setIsDragging(true);
            lastMousePosition.current = { x: e.clientX, y: e.clientY };
            if (canvasRef.current) canvasRef.current.style.cursor = 'grabbing';
        }
    }, [isSpacePressed]);

    const handleMouseMove = useCallback((e) => {
        if (isDragging) {
            const dx = e.clientX - lastMousePosition.current.x;
            const dy = e.clientY - lastMousePosition.current.y;
            setOffset(prev => ({ x: prev.x + dx, y: prev.y + dy }));
            lastMousePosition.current = { x: e.clientX, y: e.clientY };
        }
    }, [isDragging]);

    const handleMouseUp = useCallback(() => {
        setIsDragging(false);
        if (canvasRef.current) canvasRef.current.style.cursor = isSpacePressed ? 'grab' : 'default';
    }, [isSpacePressed]);

    const handleKeyDown = useCallback((e) => {
        if (e.code === 'Space') {
            e.preventDefault();
            setIsSpacePressed(true);
            if (canvasRef.current && !isDragging) canvasRef.current.style.cursor = 'grab';
        }
    }, [isDragging]);

    const handleKeyUp = useCallback((e) => {
        if (e.code === 'Space') {
            setIsSpacePressed(false);
            if (canvasRef.current && !isDragging) canvasRef.current.style.cursor = 'default';
        }
    }, [isDragging]);

    // --- 事件监听器设置 ---
    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;
        container.addEventListener('wheel', handleWheel, { passive: false });
        window.addEventListener('keydown', handleKeyDown);
        window.addEventListener('keyup', handleKeyUp);
        container.addEventListener('mousedown', handleMouseDown);
        window.addEventListener('mousemove', handleMouseMove);
        window.addEventListener('mouseup', handleMouseUp);

        return () => {
            container.removeEventListener('wheel', handleWheel);
            window.removeEventListener('keydown', handleKeyDown);
            window.removeEventListener('keyup', handleKeyUp);
            container.removeEventListener('mousedown', handleMouseDown);
            window.removeEventListener('mousemove', handleMouseMove);
            window.removeEventListener('mouseup', handleMouseUp);
        };
    }, [handleWheel, handleKeyDown, handleKeyUp, handleMouseDown, handleMouseMove, handleMouseUp]);

    // Canvas 的 Resize observer
    useEffect(() => {
        const canvas = canvasRef.current;
        const container = containerRef.current;
        if (!canvas || !container) return;

        const resizeObserver = new ResizeObserver(() => {
            const { width, height } = container.getBoundingClientRect();
            const dpr = window.devicePixelRatio || 1;
            canvas.width = width * dpr;
            canvas.height = height * dpr;
            canvas.style.width = `${width}px`;
            canvas.style.height = `${height}px`;
            const ctx = canvas.getContext('2d');
            ctx.scale(dpr, dpr);
            draw();
        });

        resizeObserver.observe(container);
        return () => resizeObserver.disconnect();
    }, [draw]);

    return (
        <div ref={containerRef} className="w-full h-full bg-gray-800">
            <canvas ref={canvasRef} className="absolute top-0 left-0 w-full h-full" />
            <div className="absolute top-2 right-4 flex flex-col gap-2 z-20">
                <p className="text-xs font-mono text-amber-400 mt-1 bg-black bg-opacity-50 px-2 py-1 rounded">
                    缩放: {scale.toFixed(3)}
                </p>
            </div>
        </div>
    );
};