import React, { useState, useRef, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';
import {
    drawNode,
    drawUnidirectionalEdge,
    drawBidirectionalEdge,
    drawCar,
    drawPallet
} from '../utils/canvasUtils';

// ====================================================================================
// --- FILE: src/components/CanvasRenderer.js ---
// 处理所有 Canvas 渲染和交互的专用组件。
// ====================================================================================
export const CanvasRenderer = forwardRef(({ mapData, dynamicElements, selectedElement, onElementClick }, ref) => {
    const canvasRef = useRef(null);
    const containerRef = useRef(null);

    const [scale, setScale] = useState(1);
    const [offset, setOffset] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [isSpacePressed, setIsSpacePressed] = useState(false);
    const lastMousePosition = useRef({ x: 0, y: 0 });

    const MIN_SCALE = 0.01;
    const MAX_SCALE = 5;
    const ZOOM_SENSITIVITY = 0.001;

    // --- 绘图逻辑 ---
    const draw = useCallback(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;
        const ctx = canvas.getContext('2d');

        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.save();
        ctx.translate(offset.x, offset.y);
        ctx.scale(scale, scale);

        // 可见性阈值
        const ARROW_VISIBILITY_THRESHOLD = 0.05;
        const NODE_VISIBILITY_THRESHOLD = 0.03;

        const { nodes, unidirectional_edges, bidirectional_edges } = mapData;

        // 绘制双向边
        bidirectional_edges.forEach(edge => {
            const startNode = nodes.get(edge.nodes[0]);
            const endNode = nodes.get(edge.nodes[1]);
            if (startNode && endNode) {
                drawBidirectionalEdge(ctx, startNode, endNode, scale, selectedElement?.data?.id === edge.id);
            }
        });

        // 绘制单向边
        unidirectional_edges.forEach(edge => {
            const startNode = nodes.get(edge.start_node_id);
            const endNode = nodes.get(edge.end_node_id);
            if (startNode && endNode) {
                if (scale >= ARROW_VISIBILITY_THRESHOLD) {
                    drawUnidirectionalEdge(ctx, startNode, endNode, scale, selectedElement?.data?.id === edge.id);
                } else {
                    // 放大后绘制简单的线
                    ctx.strokeStyle = '#3B82F6';
                    ctx.lineWidth = 2 / scale;
                    ctx.beginPath();
                    ctx.moveTo(startNode.x, startNode.y);
                    ctx.lineTo(endNode.x, endNode.y);
                    ctx.stroke();
                }
            }
        });

        // 绘制节点
        if (scale >= NODE_VISIBILITY_THRESHOLD) {
            nodes.forEach(node => {
                drawNode(ctx, node, scale, selectedElement?.data?.id === node.id);
            });
        }

        // 绘制动态元素
        dynamicElements.pallets.forEach(pallet => drawPallet(ctx, pallet, scale));
        dynamicElements.cars.forEach(car => drawCar(ctx, car, scale));

        ctx.restore();
    }, [scale, offset, mapData, dynamicElements, selectedElement]);

    useEffect(() => {
        draw();
    }, [draw]);

    // --- 视图控制 (缩放, 平移, 重置) ---
    const resetView = useCallback(() => {
        const { nodes } = mapData;
        const container = containerRef.current;
        if (!container || nodes.size === 0) return;

        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        nodes.forEach(node => {
            if (node.x < minX) minX = node.x;
            if (node.y < minY) minY = node.y;
            if (node.x > maxX) maxX = node.x;
            if (node.y > maxY) maxY = node.y;
        });

        const { width: viewWidth, height: viewHeight } = container.getBoundingClientRect();
        const mapWidth = maxX - minX;
        const mapHeight = maxY - minY;
        const mapCenterX = minX + mapWidth / 2;
        const mapCenterY = minY + mapHeight / 2;

        const scaleX = mapWidth > 0 ? (viewWidth / mapWidth) * 0.9 : 1;
        const scaleY = mapHeight > 0 ? (viewHeight / mapHeight) * 0.9 : 1;
        const initialScale = Math.min(scaleX, scaleY, MAX_SCALE);

        setOffset({
            x: viewWidth / 2 - mapCenterX * initialScale,
            y: viewHeight / 2 - mapCenterY * initialScale,
        });
        setScale(initialScale);
    }, [mapData]);

    useEffect(() => {
        // 数据加载后自动重置视图
        if (mapData.nodes.size > 0) {
            resetView();
        }
    }, [mapData.nodes, resetView]);

    // 缩放控制函数
    const zoomIn = useCallback(() => {
        const newScale = Math.min(MAX_SCALE, scale * 1.2);
        setScale(newScale);
    }, [scale]);

    const zoomOut = useCallback(() => {
        const newScale = Math.max(MIN_SCALE, scale / 1.2);
        setScale(newScale);
    }, [scale]);

    // 通过ref暴露控制方法
    useImperativeHandle(ref, () => ({
        zoomIn,
        zoomOut,
        resetView
    }), [zoomIn, zoomOut, resetView]);

    // --- 事件处理器 ---
    const handleWheel = useCallback((e) => {
        e.preventDefault();
        if (!canvasRef.current) return;
        const rect = canvasRef.current.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        const zoomFactor = 1 - e.deltaY * ZOOM_SENSITIVITY;
        const newScale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale * zoomFactor));
        const worldX = (mouseX - offset.x) / scale;
        const worldY = (mouseY - offset.y) / scale;
        setOffset({ x: mouseX - worldX * newScale, y: mouseY - worldY * newScale });
        setScale(newScale);
    }, [scale, offset]);

    const handleMouseDown = useCallback((e) => {
        if (isSpacePressed) {
            setIsDragging(true);
            lastMousePosition.current = { x: e.clientX, y: e.clientY };
            if (canvasRef.current) canvasRef.current.style.cursor = 'grabbing';
        } else {
            // 处理元素选择
            handleElementClick(e);
        }
    }, [isSpacePressed]);

    // 处理元素点击选择
    const handleElementClick = useCallback((e) => {
        if (!canvasRef.current || !onElementClick) return;

        const rect = canvasRef.current.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        // 转换为世界坐标
        const worldX = (mouseX - offset.x) / scale;
        const worldY = (mouseY - offset.y) / scale;

        // 检查是否点击了节点
        let clickedElement = null;
        const nodeClickRadius = 15 / scale; // 点击检测半径

        mapData.nodes.forEach((node, nodeId) => {
            const distance = Math.sqrt(
                Math.pow(worldX - node.x, 2) + Math.pow(worldY - node.y, 2)
            );

            if (distance <= nodeClickRadius) {
                clickedElement = {
                    type: 'node',
                    data: { ...node, id: nodeId }
                };
            }
        });

        // 如果没有点击节点，检查是否点击了边缘
        if (!clickedElement) {
            const edgeClickDistance = 10 / scale; // 边缘点击检测距离

            // 检查单向边
            mapData.unidirectional_edges?.forEach(edge => {
                const startNode = mapData.nodes.get(edge.start_node_id);
                const endNode = mapData.nodes.get(edge.end_node_id);

                if (startNode && endNode) {
                    const distanceToLine = distancePointToLine(
                        worldX, worldY,
                        startNode.x, startNode.y,
                        endNode.x, endNode.y
                    );

                    if (distanceToLine <= edgeClickDistance) {
                        clickedElement = {
                            type: 'unidirectional_edge',
                            data: { ...edge, startNode, endNode }
                        };
                    }
                }
            });

            // 检查双向边
            if (!clickedElement) {
                mapData.bidirectional_edges?.forEach(edge => {
                    const [nodeId1, nodeId2] = edge.nodes;
                    const node1 = mapData.nodes.get(nodeId1);
                    const node2 = mapData.nodes.get(nodeId2);

                    if (node1 && node2) {
                        const distanceToLine = distancePointToLine(
                            worldX, worldY,
                            node1.x, node1.y,
                            node2.x, node2.y
                        );

                        if (distanceToLine <= edgeClickDistance) {
                            clickedElement = {
                                type: 'bidirectional_edge',
                                data: { ...edge, node1, node2 }
                            };
                        }
                    }
                });
            }
        }

        onElementClick(clickedElement);
    }, [mapData, scale, offset, onElementClick]);

    // 计算点到线段的距离
    const distancePointToLine = (px, py, x1, y1, x2, y2) => {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;

        if (lenSq === 0) return Math.sqrt(A * A + B * B);

        let param = dot / lenSq;

        if (param < 0) {
            return Math.sqrt(A * A + B * B);
        } else if (param > 1) {
            const E = px - x2;
            const F = py - y2;
            return Math.sqrt(E * E + F * F);
        } else {
            const projX = x1 + param * C;
            const projY = y1 + param * D;
            const G = px - projX;
            const H = py - projY;
            return Math.sqrt(G * G + H * H);
        }
    };

    const handleMouseMove = useCallback((e) => {
        if (isDragging) {
            const dx = e.clientX - lastMousePosition.current.x;
            const dy = e.clientY - lastMousePosition.current.y;
            setOffset(prev => ({ x: prev.x + dx, y: prev.y + dy }));
            lastMousePosition.current = { x: e.clientX, y: e.clientY };
        }
    }, [isDragging]);

    const handleMouseUp = useCallback(() => {
        setIsDragging(false);
        if (canvasRef.current) canvasRef.current.style.cursor = isSpacePressed ? 'grab' : 'default';
    }, [isSpacePressed]);

    const handleKeyDown = useCallback((e) => {
        if (e.code === 'Space') {
            e.preventDefault();
            setIsSpacePressed(true);
            if (canvasRef.current && !isDragging) canvasRef.current.style.cursor = 'grab';
        }
    }, [isDragging]);

    const handleKeyUp = useCallback((e) => {
        if (e.code === 'Space') {
            setIsSpacePressed(false);
            if (canvasRef.current && !isDragging) canvasRef.current.style.cursor = 'default';
        }
    }, [isDragging]);

    // --- 事件监听器设置 ---
    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;
        container.addEventListener('wheel', handleWheel, { passive: false });
        window.addEventListener('keydown', handleKeyDown);
        window.addEventListener('keyup', handleKeyUp);
        container.addEventListener('mousedown', handleMouseDown);
        window.addEventListener('mousemove', handleMouseMove);
        window.addEventListener('mouseup', handleMouseUp);

        return () => {
            container.removeEventListener('wheel', handleWheel);
            window.removeEventListener('keydown', handleKeyDown);
            window.removeEventListener('keyup', handleKeyUp);
            container.removeEventListener('mousedown', handleMouseDown);
            window.removeEventListener('mousemove', handleMouseMove);
            window.removeEventListener('mouseup', handleMouseUp);
        };
    }, [handleWheel, handleKeyDown, handleKeyUp, handleMouseDown, handleMouseMove, handleMouseUp]);

    // Canvas 的 Resize observer
    useEffect(() => {
        const canvas = canvasRef.current;
        const container = containerRef.current;
        if (!canvas || !container) return;

        const resizeObserver = new ResizeObserver(() => {
            const { width, height } = container.getBoundingClientRect();
            const dpr = window.devicePixelRatio || 1;
            canvas.width = width * dpr;
            canvas.height = height * dpr;
            canvas.style.width = `${width}px`;
            canvas.style.height = `${height}px`;
            const ctx = canvas.getContext('2d');
            ctx.scale(dpr, dpr);
            draw();
        });

        resizeObserver.observe(container);
        return () => resizeObserver.disconnect();
    }, [draw]);

    return (
        <div ref={containerRef} className="w-full h-full bg-gray-800">
            <canvas ref={canvasRef} className="absolute top-0 left-0 w-full h-full" />
            <div className="absolute top-2 right-4 flex flex-col gap-2 z-20">
                <p className="text-xs font-mono text-amber-400 mt-1 bg-black bg-opacity-50 px-2 py-1 rounded">
                    缩放: {scale.toFixed(3)}
                </p>
            </div>
        </div>
    );
});