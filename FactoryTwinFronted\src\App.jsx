import React, { useState, useRef } from 'react';
import { Sidebar } from './components/Sidebar';
import { CanvasRenderer } from './components/CanvasRenderer';
import { Timeline } from './components/Timeline';
import { useMapData } from './hooks/useMapData';
import { useSimulation } from './hooks/useSimulation';

// ====================================================================================
// --- FILE: src/App.js ---
// 这是主应用组件。它协调所有其他组件和状态。
// ====================================================================================
function FactoryCanvasApp() {
    const { mapData, isLoading } = useMapData('/test_map_data.json');
    const {
        currentTime,
        isPlaying,
        play,
        pause,
        setTime,
        setRoute,
        simulationData,
        dynamicElements
    } = useSimulation(mapData);

    // 目前，选定元素的逻辑已简化。可以扩展。
    const [selectedElement, setSelectedElement] = useState(null);

    // 视图控制引用 - 用于从外部控制CanvasRenderer的视图
    const canvasControlsRef = useRef(null);

    const handleSetRoute = (startNodeId, endNodeId) => {
        setRoute(startNodeId, endNodeId);
    };

    // 视图控制函数
    const handleZoomIn = () => {
        if (canvasControlsRef.current?.zoomIn) {
            canvasControlsRef.current.zoomIn();
        }
    };

    const handleZoomOut = () => {
        if (canvasControlsRef.current?.zoomOut) {
            canvasControlsRef.current.zoomOut();
        }
    };

    const handleResetView = () => {
        if (canvasControlsRef.current?.resetView) {
            canvasControlsRef.current.resetView();
        }
    };

    if (isLoading) {
        return (
            <div className="w-full h-full flex items-center justify-center bg-gray-800">
                <div className="text-white text-2xl animate-pulse">正在加载地图数据...</div>
            </div>
        );
    }

    return (
        <div className="w-full h-full font-sans relative overflow-hidden bg-gray-800">
            <Sidebar
                mapData={mapData}
                onSetRoute={handleSetRoute}
                onZoomIn={handleZoomIn}
                onZoomOut={handleZoomOut}
                onResetView={handleResetView}
                selectedElement={selectedElement}
            />

            <main className="absolute top-0 left-80 right-0 bottom-0">
                <CanvasRenderer
                    ref={canvasControlsRef}
                    mapData={mapData}
                    dynamicElements={dynamicElements}
                    selectedElement={selectedElement}
                    onElementClick={setSelectedElement}
                />
            </main>
            
            <Timeline 
                currentTime={currentTime}
                maxTime={simulationData.route?.totalTime || 0}
                isPlaying={isPlaying}
                onPlay={play}
                onPause={pause}
                onTimeChange={setTime}
            />
        </div>
    );
}


// --- 应用的主导出 ---
export default function App() {
    return (
        <div style={{ width: '100vw', height: '100vh' }}>
            <FactoryCanvasApp />
        </div>
    );
}
