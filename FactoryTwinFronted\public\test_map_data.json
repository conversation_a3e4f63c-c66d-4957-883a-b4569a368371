{"graph_info": {"id": "test_factory", "name": "测试工厂布局", "description": "用于演示的简化工厂数字孪生地图"}, "nodes": [{"id": "A1", "x": 100, "y": 100, "type": "storage", "name": "仓储区A1", "deviceType": "Storage_Rack", "capacity": 50}, {"id": "A2", "x": 300, "y": 100, "type": "storage", "name": "仓储区A2", "deviceType": "Storage_Rack", "capacity": 50}, {"id": "B1", "x": 100, "y": 200, "type": "workstation", "name": "工作站B1", "deviceType": "Assembly_Station", "status": "active"}, {"id": "B2", "x": 300, "y": 200, "type": "workstation", "name": "工作站B2", "deviceType": "Assembly_Station", "status": "active"}, {"id": "C1", "x": 200, "y": 150, "type": "junction", "name": "交叉点C1", "deviceType": "Path_Junction"}, {"id": "C2", "x": 500, "y": 150, "type": "junction", "name": "交叉点C2", "deviceType": "Path_Junction"}, {"id": "D1", "x": 400, "y": 100, "type": "loading", "name": "装载区D1", "deviceType": "Loading_Dock", "capacity": 10}, {"id": "D2", "x": 400, "y": 200, "type": "loading", "name": "装载区D2", "deviceType": "Loading_Dock", "capacity": 10}, {"id": "E1", "x": 600, "y": 100, "type": "shipping", "name": "发货区E1", "deviceType": "Shipping_Dock"}, {"id": "E2", "x": 600, "y": 200, "type": "shipping", "name": "发货区E2", "deviceType": "Shipping_Dock"}], "unidirectional_edges": [{"id": "edge_A1_C1", "start_node_id": "A1", "end_node_id": "C1", "distance": 111.8, "travel_time": 5.0, "direction": "outbound"}, {"id": "edge_A2_C1", "start_node_id": "A2", "end_node_id": "C1", "distance": 111.8, "travel_time": 5.0, "direction": "outbound"}, {"id": "edge_C1_B1", "start_node_id": "C1", "end_node_id": "B1", "distance": 111.8, "travel_time": 5.0, "direction": "inbound"}, {"id": "edge_C1_B2", "start_node_id": "C1", "end_node_id": "B2", "distance": 111.8, "travel_time": 5.0, "direction": "inbound"}, {"id": "edge_B1_D2", "start_node_id": "B1", "end_node_id": "D2", "distance": 316.2, "travel_time": 8.0, "direction": "outbound"}, {"id": "edge_B2_D1", "start_node_id": "B2", "end_node_id": "D1", "distance": 141.4, "travel_time": 6.0, "direction": "outbound"}], "bidirectional_edges": [{"id": "path_C1_C2", "nodes": ["C1", "C2"], "distance": 300.0, "travel_time": 10.0, "type": "main_corridor"}, {"id": "path_D1_C2", "nodes": ["D1", "C2"], "distance": 111.8, "travel_time": 5.0, "type": "connector"}, {"id": "path_D2_C2", "nodes": ["D2", "C2"], "distance": 111.8, "travel_time": 5.0, "type": "connector"}, {"id": "path_C2_E1", "nodes": ["C2", "E1"], "distance": 111.8, "travel_time": 5.0, "type": "exit_path"}, {"id": "path_C2_E2", "nodes": ["C2", "E2"], "distance": 111.8, "travel_time": 5.0, "type": "exit_path"}], "pallets": [{"id": "pallet_001", "type": "standard", "status": "loaded", "location": "A1", "contents": "电子元件", "weight": 25.5, "shape": "standard"}, {"id": "pallet_002", "type": "heavy_duty", "status": "empty", "location": "A2", "contents": "", "weight": 5.0, "shape": "large"}, {"id": "pallet_003", "type": "standard", "status": "in_transit", "location": "C1", "contents": "机械零件", "weight": 18.2, "shape": "standard"}, {"id": "pallet_004", "type": "long", "status": "loaded", "location": "B1", "contents": "长型材料", "weight": 35.8, "shape": "long"}, {"id": "pallet_005", "type": "standard", "status": "ready", "location": "D1", "contents": "成品组件", "weight": 22.1, "shape": "standard"}, {"id": "pallet_006", "type": "heavy_duty", "status": "loaded", "location": "D2", "contents": "重型设备", "weight": 85.5, "shape": "large"}], "vehicles": [{"id": "agv_001", "type": "standard_agv", "status": "idle", "location": "C1", "battery_level": 85, "max_payload": 100, "current_load": 0}, {"id": "agv_002", "type": "heavy_duty_agv", "status": "active", "location": "B1", "battery_level": 92, "max_payload": 200, "current_load": 25}, {"id": "agv_003", "type": "standard_agv", "status": "charging", "location": "A1", "battery_level": 15, "max_payload": 100, "current_load": 0}, {"id": "agv_004", "type": "compact_agv", "status": "active", "location": "C2", "battery_level": 78, "max_payload": 50, "current_load": 18}], "simulation_scenarios": [{"id": "scenario_001", "name": "标准物料运输", "description": "从仓储区到工作站的标准物料运输流程", "routes": [{"vehicle_id": "agv_001", "start": "A1", "end": "B1", "cargo": "pallet_001", "priority": "normal"}, {"vehicle_id": "agv_002", "start": "A2", "end": "B2", "cargo": "pallet_002", "priority": "high"}]}, {"id": "scenario_002", "name": "成品发货流程", "description": "从工作站到发货区的成品运输", "routes": [{"vehicle_id": "agv_001", "start": "D1", "end": "E1", "cargo": "pallet_005", "priority": "urgent"}, {"vehicle_id": "agv_004", "start": "D2", "end": "E2", "cargo": "pallet_006", "priority": "normal"}]}]}