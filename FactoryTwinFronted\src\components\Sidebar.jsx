import React, { useState, useEffect, useMemo } from 'react';
import { Accordion } from './Accordion';

// Icon Components
const ZoomInIcon = React.memo(() => (<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="11" y1="8" x2="11" y2="14"></line><line x1="8" y1="11" x2="14" y2="11"></line></svg>));
const ZoomOutIcon = React.memo(() => (<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="8" y1="11" x2="14" y2="11"></line></svg>));
const ResetIcon = React.memo(() => (<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 2v6h6"></path><path d="M21 12A9 9 0 0 0 6 5.3L3 8"></path><path d="M21 22v-6h-6"></path><path d="M3 12a9 9 0 0 0 15 6.7l3-2.7"></path></svg>));

// ====================================================================================
// --- FILE: src/components/Sidebar.js ---
// 新的侧边栏组件，用于所有控件。
// ====================================================================================
export const Sidebar = ({ mapData, onZoomIn, onZoomOut, onResetView, onSetRoute, selectedElement }) => {
    const [startNode, setStartNode] = useState('');
    const [endNode, setEndNode] = useState('');

    const nodeOptions = useMemo(() => 
        Array.from(mapData.nodes.keys()).sort().map(nodeId => (
            <option key={nodeId} value={nodeId}>{nodeId}</option>
        )), [mapData.nodes]);
        
    useEffect(() => {
       const nodeIds = Array.from(mapData.nodes.keys()).sort();
       if(nodeIds.length >= 2) {
           setStartNode(nodeIds[0]);
           setEndNode(nodeIds[1]);
       }
    }, [mapData.nodes]);

    const handleCalculateRoute = () => {
        if (startNode && endNode && startNode !== endNode) {
            onSetRoute(startNode, endNode);
        } else {
            alert("请为路线选择两个不同的节点。");
        }
    };

    return (
        <div className="absolute top-0 left-0 h-full w-80 bg-gray-900 text-white shadow-lg z-30 flex flex-col">
            <div className="p-4 border-b border-gray-700">
                <h1 className="text-xl font-bold">FactoryTwin-View</h1>
                <p className="text-sm text-gray-400">v2.0 模拟</p>
            </div>

            <div className="flex-grow overflow-y-auto">
                <Accordion title="视图控件">
                    <div className="flex justify-around gap-2">
                        <button onClick={onZoomIn} title="放大" className="flex-1 bg-gray-700 p-2 rounded-lg shadow-lg hover:bg-gray-600 transition-colors duration-200 flex justify-center"><ZoomInIcon /></button>
                        <button onClick={onZoomOut} title="缩小" className="flex-1 bg-gray-700 p-2 rounded-lg shadow-lg hover:bg-gray-600 transition-colors duration-200 flex justify-center"><ZoomOutIcon /></button>
                        <button onClick={onResetView} title="重置视图" className="flex-1 bg-gray-700 p-2 rounded-lg shadow-lg hover:bg-gray-600 transition-colors duration-200 flex justify-center"><ResetIcon /></button>
                    </div>
                </Accordion>

                <Accordion title="路线规划">
                    <div className="space-y-3">
                         <div>
                            <label htmlFor="start-node" className="block text-sm font-medium text-gray-300">起始节点</label>
                            <select id="start-node" value={startNode} onChange={e => setStartNode(e.target.value)} className="mt-1 block w-full pl-3 pr-10 py-2 text-base bg-gray-700 border-gray-600 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                {nodeOptions}
                            </select>
                        </div>
                         <div>
                            <label htmlFor="end-node" className="block text-sm font-medium text-gray-300">结束节点</label>
                            <select id="end-node" value={endNode} onChange={e => setEndNode(e.target.value)} className="mt-1 block w-full pl-3 pr-10 py-2 text-base bg-gray-700 border-gray-600 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                               {nodeOptions}
                            </select>
                        </div>
                        <button onClick={handleCalculateRoute} className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg">
                            规划路线
                        </button>
                    </div>
                </Accordion>
                
                <Accordion title="模拟控件">
                     <p className="text-sm text-gray-400">
                        规划路线后，使用底部的时间轴来控制模拟。
                     </p>
                </Accordion>

                {selectedElement && (
                    <Accordion title="选中元素">
                        <div className="space-y-2">
                            <div className="text-sm">
                                <span className="font-medium text-gray-300">类型:</span>
                                <span className="ml-2 text-white">{selectedElement.type || '节点'}</span>
                            </div>
                            <div className="text-sm">
                                <span className="font-medium text-gray-300">ID:</span>
                                <span className="ml-2 text-white font-mono">{selectedElement.data?.id}</span>
                            </div>
                            {selectedElement.data?.x && selectedElement.data?.y && (
                                <div className="text-sm">
                                    <span className="font-medium text-gray-300">坐标:</span>
                                    <span className="ml-2 text-white font-mono">
                                        ({selectedElement.data.x.toFixed(0)}, {selectedElement.data.y.toFixed(0)})
                                    </span>
                                </div>
                            )}
                            {selectedElement.data?.deviceType && (
                                <div className="text-sm">
                                    <span className="font-medium text-gray-300">设备类型:</span>
                                    <span className="ml-2 text-white text-xs">{selectedElement.data.deviceType}</span>
                                </div>
                            )}
                        </div>
                    </Accordion>
                )}

            </div>
        </div>
    );
};
