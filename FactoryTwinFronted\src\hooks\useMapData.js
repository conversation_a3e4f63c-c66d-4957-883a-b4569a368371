import { useState, useEffect } from 'react';

// ====================================================================================
// --- FILE: src/hooks/useMapData.js ---
// 这个自定义 Hook 负责获取和准备静态地图数据。
// ====================================================================================
export const useMapData = (url) => {
    const [mapData, setMapData] = useState({
        nodes: new Map(),
        unidirectional_edges: [],
        bidirectional_edges: [],
        pallets: [],
        vehicles: [],
        graph_info: {}
    });
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const loadMapData = async () => {
            setIsLoading(true);
            try {
                // 在真实应用中，您可能会从动态URL获取。
                // 现在，我们假设它在 public 文件夹中。
                const response = await fetch(url);
                if (!response.ok) throw new Error(`无法加载地图数据: ${response.statusText}`);
                const data = await response.json();

                // 处理节点数据 - 如果是新格式则不需要翻转Y轴，如果是旧格式则翻转
                let transformedNodes;
                if (data.graph_info && data.graph_info.id === 'test_factory') {
                    // 新的测试数据格式，坐标已经是正确的
                    transformedNodes = data.nodes;
                } else {
                    // 旧的数据格式，需要翻转Y轴
                    transformedNodes = data.nodes.map(node => ({ ...node, y: -node.y }));
                }

                const nodesMap = new Map(transformedNodes.map(node => [node.id, node]));

                setMapData({
                    nodes: nodesMap,
                    unidirectional_edges: data.unidirectional_edges || [],
                    bidirectional_edges: data.bidirectional_edges || [],
                    pallets: data.pallets || [],
                    vehicles: data.vehicles || [],
                    graph_info: data.graph_info || {}
                });
            } catch (error) {
                console.error("解析地图JSON时出错:", error);
            } finally {
                setIsLoading(false);
            }
        };
        loadMapData();
    }, [url]);

    return { mapData, isLoading };
};
